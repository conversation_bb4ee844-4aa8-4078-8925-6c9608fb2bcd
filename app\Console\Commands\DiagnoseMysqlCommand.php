<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class DiagnoseMysqlCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'db:diagnose-mysql';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Diagnostique la configuration MySQL et teste la compatibilité';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔍 Diagnostic de la configuration MySQL...');
        $this->newLine();

        try {
            // Test de connexion
            $this->info('1. Test de connexion à la base de données...');
            $connection = DB::connection();
            $pdo = $connection->getPdo();
            $this->info('   ✅ Connexion réussie');

            // Version MySQL/MariaDB
            $this->info('2. Vérification de la version...');
            $version = DB::select('SELECT VERSION() as version')[0]->version;
            $this->info("   📊 Version: {$version}");

            // Test du mode SQL actuel
            $this->info('3. Vérification du mode SQL actuel...');
            $sqlMode = DB::select('SELECT @@sql_mode as mode')[0]->mode;
            $this->info("   ⚙️  Mode SQL: {$sqlMode}");

            // Test des modes SQL supportés
            $this->info('4. Test des modes SQL...');
            $testModes = [
                'STRICT_TRANS_TABLES,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION',
                'STRICT_TRANS_TABLES,ERROR_FOR_DIVISION_BY_ZERO,NO_AUTO_CREATE_USER,NO_ENGINE_SUBSTITUTION',
                'STRICT_TRANS_TABLES'
            ];

            foreach ($testModes as $mode) {
                try {
                    DB::statement("SET SESSION sql_mode = '{$mode}'");
                    $this->info("   ✅ Mode supporté: {$mode}");
                } catch (\Exception $e) {
                    $this->error("   ❌ Mode non supporté: {$mode}");
                    $this->error("      Erreur: " . $e->getMessage());
                }
            }

            // Test de la table cache
            $this->info('5. Test de la table cache...');
            try {
                $cacheCount = DB::table('cache')->count();
                $this->info("   ✅ Table cache accessible ({$cacheCount} entrées)");
            } catch (\Exception $e) {
                $this->error('   ❌ Problème avec la table cache');
                $this->error("      Erreur: " . $e->getMessage());
            }

            // Recommandations
            $this->newLine();
            $this->info('📋 Recommandations:');
            
            if (str_contains(strtolower($version), 'mariadb')) {
                $this->info('   • Vous utilisez MariaDB - mode SQL ajusté automatiquement');
            } elseif (version_compare($this->extractMysqlVersion($version), '8.0', '>=')) {
                $this->info('   • MySQL 8.0+ détecté - NO_AUTO_CREATE_USER supprimé automatiquement');
            }

            $this->info('   • Configuration optimisée pour votre version');
            $this->info('   • Cache configuré pour utiliser la base de données');

        } catch (\Exception $e) {
            $this->error('❌ Erreur lors du diagnostic:');
            $this->error($e->getMessage());
            
            $this->newLine();
            $this->info('🔧 Solutions suggérées:');
            $this->info('1. Vérifiez que MySQL/MariaDB est démarré');
            $this->info('2. Vérifiez les paramètres de connexion dans .env');
            $this->info('3. Exécutez: php artisan config:clear');
            $this->info('4. Exécutez: php artisan cache:clear');
            
            return 1;
        }

        $this->newLine();
        $this->info('✅ Diagnostic terminé avec succès!');
        return 0;
    }

    /**
     * Extract MySQL version number from version string
     */
    private function extractMysqlVersion(string $version): string
    {
        preg_match('/(\d+\.\d+\.\d+)/', $version, $matches);
        return $matches[1] ?? '5.7.0';
    }
}
