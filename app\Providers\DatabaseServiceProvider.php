<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use PDO;

class DatabaseServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        $this->configureMySQLMode();
        $this->configureConnectionOptimizations();
    }

    /**
     * Configure MySQL mode based on server version
     */
    private function configureMySQLMode(): void
    {
        try {
            // Vérifier si nous utilisons MySQL
            if (config('database.default') === 'mysql') {
                DB::listen(function ($query) {
                    // Log des requêtes lentes en développement
                    if (app()->environment('local') && $query->time > 1000) {
                        Log::warning('Requête lente détectée', [
                            'sql' => $query->sql,
                            'time' => $query->time . 'ms'
                        ]);
                    }
                });

                // Configuration post-connexion pour MySQL
                DB::connection()->getPdo()->exec("SET SESSION sql_mode='STRICT_TRANS_TABLES,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION'");
            }
        } catch (\Exception $e) {
            // En cas d'erreur, utiliser un mode SQL plus permissif
            try {
                DB::connection()->getPdo()->exec("SET SESSION sql_mode='STRICT_TRANS_TABLES'");
                Log::warning('Mode SQL ajusté pour compatibilité', [
                    'error' => $e->getMessage()
                ]);
            } catch (\Exception $fallbackError) {
                Log::error('Impossible de configurer le mode SQL', [
                    'error' => $fallbackError->getMessage()
                ]);
            }
        }
    }

    /**
     * Configure database connection optimizations
     */
    private function configureConnectionOptimizations(): void
    {
        if (config('database.default') === 'mysql') {
            try {
                $pdo = DB::connection()->getPdo();
                
                // Optimisations de performance
                $pdo->exec("SET SESSION innodb_lock_wait_timeout = 50");
                $pdo->exec("SET SESSION wait_timeout = 28800");
                $pdo->exec("SET SESSION interactive_timeout = 28800");
                
                // Configuration pour l'UTF-8
                $pdo->exec("SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci");
                
            } catch (\Exception $e) {
                Log::warning('Impossible d\'appliquer les optimisations de base de données', [
                    'error' => $e->getMessage()
                ]);
            }
        }
    }
}
